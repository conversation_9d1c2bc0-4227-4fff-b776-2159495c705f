# Build stage
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the binary
ARG VERSION=dev
ARG COMMIT=unknown
ARG DATE=unknown
RUN CGO_ENABLED=0 GOOS=linux go build \
    -ldflags "-w -s -X main.version=${VERSION} -X main.commit=${COMMIT} -X main.date=${DATE}" \
    -o jfrog-cli .

# Final stage
FROM alpine:3.19

# Install runtime dependencies
RUN apk add --no-cache ca-certificates tzdata curl

# Create non-root user
RUN addgroup -g 1001 jfrog && \
    adduser -D -u 1001 -G jfrog jfrog

# Copy binary from builder stage
COPY --from=builder /app/jfrog-cli /usr/local/bin/jfrog-cli

# Create config directory
RUN mkdir -p /home/<USER>/.config && \
    chown -R jfrog:jfrog /home/<USER>

# Switch to non-root user
USER jfrog

# Set working directory
WORKDIR /home/<USER>

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD jfrog-cli system ping || exit 1

# Default command
ENTRYPOINT ["jfrog-cli"]
CMD ["--help"]

# Labels
LABEL maintainer="<EMAIL>"
LABEL org.opencontainers.image.title="JFrog Artifactory CLI"
LABEL org.opencontainers.image.description="A comprehensive CLI for JFrog Artifactory"
LABEL org.opencontainers.image.url="https://github.com/yourorg/jfrog-cli"
LABEL org.opencontainers.image.source="https://github.com/yourorg/jfrog-cli"
LABEL org.opencontainers.image.vendor="Your Organization"
LABEL org.opencontainers.image.licenses="MIT"