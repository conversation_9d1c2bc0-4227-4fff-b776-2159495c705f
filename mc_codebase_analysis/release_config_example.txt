# Enterprise Release Configuration - World Standard
# This configuration demonstrates a complete enterprise release process
# with progressive rollouts, automated rollbacks, and comprehensive safety measures

# Release identification and metadata
release_name: "jfrog-enterprise-cli"
version: "3.1.0"
previous_version: "3.0.5"
environment: "production"
namespace: "jfrog-enterprise"

# Release strategy configuration
strategy:
  type: "canary"  # Options: canary, blue-green, rolling, recreate
  progress_deadline: "30m"
  auto_promote: false  # Require manual promotion for production
  auto_rollback: true  # Auto-rollback on failure
  
  # Canary deployment configuration
  canary_config:
    steps:
      # Step 1: Deploy canary with minimal traffic
      - set_weight: 5
        pause:
          duration: "5m"
      
      # Step 2: Analysis phase
      - analysis:
          templates:
            - "success-rate-analysis"
            - "latency-analysis" 
            - "error-rate-analysis"
          duration: "10m"
          success_conditions:
            - "success_rate > 0.99"
            - "latency_p99 < 500ms"
            - "error_rate < 0.01"
          failure_conditions:
            - "success_rate < 0.95"
            - "latency_p99 > 1000ms"
            - "error_rate > 0.05"
      
      # Step 3: Increase traffic gradually
      - set_weight: 25
        pause:
          duration: "10m"
      
      # Step 4: Second analysis phase
      - analysis:
          templates:
            - "success-rate-analysis"
            - "resource-utilization-analysis"
          duration: "15m"
      
      # Step 5: Scale to majority traffic
      - set_weight: 75
        pause:
          duration: "15m"
      
      # Step 6: Final analysis before full promotion
      - analysis:
          templates:
            - "comprehensive-analysis"
          duration: "20m"
      
      # Step 7: Full promotion (requires manual approval in prod)
      - set_weight: 100
    
    # Traffic routing configuration
    traffic_routing:
      istio:
        virtual_service: "jfrog-enterprise-vs"
        destination_rule: "jfrog-enterprise-dr"
        match:
          - headers:
              canary:
                exact: "true"
          - headers:
              user-type:
                exact: "beta"
    
    # Analysis template reference
    analysis_template: "comprehensive-canary-analysis"
    
    # Success and failure conditions
    success_conditions:
      - "success_rate >= 0.99"
      - "latency_p99 <= 500ms"
      - "cpu_utilization <= 0.7"
      - "memory_utilization <= 0.8"
      - "error_budget_remaining > 0.1"
    
    failure_conditions:
      - "success_rate < 0.95"
      - "latency_p99 > 1000ms"
      - "error_rate > 0.05"
      - "cpu_utilization > 0.9"
      - "memory_utilization > 0.95"
    
    # Timing configuration
    scale_down_delay: "30s"
    abort_scale_down_delay: "30s"

  # Traffic splitting configuration for multiple ingress controllers
  traffic_splitting:
    istio:
      virtual_service: "jfrog-enterprise-vs"
      destination_rule: "jfrog-enterprise-dr"
      match_rules:
        - headers:
            canary: "true"
        - headers:
            user-agent:
              regex: ".*mobile.*"
        - uri:
            prefix: "/api/v2"
    
    nginx:
      ingress_class: "nginx"
      annotations:
        nginx.ingress.kubernetes.io/canary: "true"
        nginx.ingress.kubernetes.io/canary-weight: "0"
        nginx.ingress.kubernetes.io/canary-by-header: "canary"
    
    alb:
      target_group_arn: "arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/jfrog-enterprise-canary/1234567890123456"
      listener_rule_priority: 100

# Artifact management configuration
artifacts:
  # Primary application artifacts
  application:
    - name: "jfrog-enterprise-cli"
      type: "binary"
      repository: "enterprise-releases"
      path: "jfrog-enterprise-cli/3.1.0/jfrog-enterprise-cli-linux-amd64"
      checksum: "sha256:abcd1234567890ef..."
      signature: "cosign-signature"
      slsa_provenance: true
    
    - name: "jfrog-enterprise-cli-arm64"
      type: "binary" 
      repository: "enterprise-releases"
      path: "jfrog-enterprise-cli/3.1.0/jfrog-enterprise-cli-linux-arm64"
      checksum: "sha256:ef1234567890abcd..."
      signature: "cosign-signature"
      slsa_provenance: true
  
  # Container images
  containers:
    - name: "jfrog-enterprise"
      registry: "docker-registry-prod"
      repository: "enterprise/jfrog-cli"
      tag: "3.1.0"
      digest: "sha256:1234567890abcdef..."
      platforms:
        - "linux/amd64"
        - "linux/arm64"
      signed: true
      sbom_included: true
      vulnerability_scan: "passed"
  
  # Helm charts
  helm_charts:
    - name: "jfrog-enterprise-chart"
      repository: "helm-charts-prod"
      version: "3.1.0"
      app_version: "3.1.0"
      values_schema_version: "v1"
      signed: true
  
  # Configuration artifacts
  configurations:
    - name: "application-config"
      type: "configmap"
      version: "3.1.0"
      checksum: "sha256:config123456..."
    
    - name: "security-policies"
      type: "policy-bundle"
      version: "1.2.0"
      checksum: "sha256:policy789012..."

# Deployment configuration
deployment:
  # Target environments and clusters
  targets:
    - cluster: "prod-us-east-1"
      namespace: "jfrog-enterprise"
      region: "us-east-1"
      replicas: 5
      resources:
        requests:
          cpu: "500m"
          memory: "1Gi"
        limits:
          cpu: "2000m"
          memory: "4Gi"
    
    - cluster: "prod-eu-west-1"
      namespace: "jfrog-enterprise"
      region: "eu-west-1"
      replicas: 3
      resources:
        requests:
          cpu: "500m"
          memory: "1Gi"
        limits:
          cpu: "2000m"
          memory: "4Gi"
  
  # Kubernetes deployment settings
  kubernetes:
    deployment_strategy: "RollingUpdate"
    max_unavailable: "25%"
    max_surge: "25%"
    revision_history_limit: 10
    progress_deadline_seconds: 600
    
    # Pod disruption budget
    pod_disruption_budget:
      min_available: "50%"
    
    # Horizontal Pod Autoscaler
    hpa:
      enabled: true
      min_replicas: 3
      max_replicas: 20
      target_cpu_utilization: 70
      target_memory_utilization: 80
      scale_down_stabilization: "5m"
      scale_up_stabilization: "3m"
    
    # Vertical Pod Autoscaler
    vpa:
      enabled: true
      update_mode: "Auto"
      resource_policy:
        container_policies:
          - container_name: "jfrog-enterprise"
            max_allowed:
              cpu: "4000m"
              memory: "8Gi"
            min_allowed:
              cpu: "100m"
              memory: "128Mi"
  
  # Service mesh configuration
  service_mesh:
    istio:
      enabled: true
      sidecar_injection: true
      mtls_mode: "STRICT"
      destination_rule:
        traffic_policy:
          connection_pool:
            tcp:
              max_connections: 100
            http:
              http1_max_pending_requests: 50
              http2_max_requests: 100
              max_requests_per_connection: 2
              max_retries: 3
          circuit_breaker:
            consecutive_gateway_errors: 5
            consecutive_5xx_errors: 5
            interval: "30s"
            base_ejection_time: "30s"
            max_ejection_percent: 50
  
  # Ingress configuration
  ingress:
    class: "nginx"
    annotations:
      cert-manager.io/cluster-issuer: "letsencrypt-prod"
      nginx.ingress.kubernetes.io/rate-limit: "100"
      nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    hosts:
      - host: "jfrog-cli.enterprise.com"
        paths:
          - path: "/"
            path_type: "Prefix"
    tls:
      - hosts:
          - "jfrog-cli.enterprise.com"
        secret_name: "jfrog-cli-tls"

# Monitoring and observability configuration
monitoring:
  # Health checks
  health_checks:
    - name: "http-health"
      type: "http"
      endpoint: "/health"
      interval: "10s"
      timeout: "5s"
      success_threshold: 1
      failure_threshold: 3
    
    - name: "tcp-health"
      type: "tcp"
      port: 8080
      interval: "5s"
      timeout: "3s"
    
    - name: "custom-health"
      type: "exec"
      command: ["/usr/local/bin/health-check"]
      interval: "30s"
      timeout: "10s"
  
  # Metrics and alerting
  metrics:
    # Prometheus metrics
    prometheus:
      enabled: true
      port: 9090
      path: "/metrics"
      scrape_interval: "15s"
      
      # Custom metrics for analysis
      custom_metrics:
        - name: "success_rate"
          query: "rate(http_requests_total{status!~\"5.*\"}[5m]) / rate(http_requests_total[5m])"
          threshold: 0.99
        
        - name: "latency_p99"
          query: "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))"
          threshold: 0.5
        
        - name: "error_rate"
          query: "rate(http_requests_total{status=~\"5.*\"}[5m]) / rate(http_requests_total[5m])"
          threshold: 0.01
        
        - name: "cpu_utilization"
          query: "rate(container_cpu_usage_seconds_total[5m])"
          threshold: 0.7
        
        - name: "memory_utilization"
          query: "container_memory_working_set_bytes / container_spec_memory_limit_bytes"
          threshold: 0.8
    
    # Jaeger tracing
    jaeger:
      enabled: true
      agent_endpoint: "jaeger-agent.observability.svc.cluster.local:6831"
      collector_endpoint: "jaeger-collector.observability.svc.cluster.local:14268"
      sampling_rate: 0.1
    
    # DataDog integration
    datadog:
      enabled: true
      api_key_secret: "datadog-api-key"
      tags:
        - "service:jfrog-enterprise"
        - "version:3.1.0"
        - "environment:production"
  
  # Alerting configuration
  alerts:
    - name: "high-error-rate"
      condition: "error_rate > 0.05"
      duration: "5m"
      severity: "critical"
      notifications:
        - "slack"
        - "pagerduty"
    
    - name: "high-latency"
      condition: "latency_p99 > 1.0"
      duration: "10m"
      severity: "warning"
      notifications:
        - "slack"
    
    - name: "deployment-failure"
      condition: "deployment_failed == 1"
      duration: "1m"
      severity: "critical"
      notifications:
        - "slack"
        - "pagerduty"
        - "email"

# Rollback configuration
rollback:
  # Automatic rollback triggers
  auto_rollback:
    enabled: true
    triggers:
      - condition: "error_rate > 0.1"
        duration: "2m"
      - condition: "success_rate < 0.9"
        duration: "5m"
      - condition: "latency_p99 > 2.0"
        duration: "10m"
    
    # Rollback strategy
    strategy: "immediate"  # immediate, gradual
    timeout: "10m"
    
    # Pre-rollback checks
    pre_checks:
      - "verify_snapshot_availability"
      - "check_rollback_permissions"
      - "validate_target_version"
    
    # Post-rollback validation
    post_validation:
      - "health_check_passed"
      - "metrics_within_threshold"
      - "integration_tests_passed"
  
  # Manual rollback configuration
  manual_rollback:
    confirmation_required: true
    approvers:
      - "platform-team"
      - "sre-team"
    approval_timeout: "30m"
    
    # Rollback options
    options:
      - type: "version_rollback"
        description: "Rollback to previous version"
        target_version: "3.0.5"
      
      - type: "snapshot_rollback"
        description: "Rollback to specific snapshot"
        snapshot_selectable: true
      
      - type: "partial_rollback"
        description: "Rollback specific components only"
        components_selectable: true

# Upgrade path definitions
upgrade_paths:
  # Upgrade from 3.0.x to 3.1.0
  - from_version: "3.0.*"
    to_version: "3.1.0"
    
    # Compatibility checks
    compatibility_check:
      api_versions:
        - name: "core_api"
          from: "v1"
          to: "v1"
          compatible: true
        
        - name: "plugin_api" 
          from: "v2"
          to: "v3"
          compatible: true
          migration_required: true
      
      database_schema:
        migration_required: true
        migration_scripts:
          - "001_add_slsa_fields.sql"
          - "002_update_indexes.sql"
        backup_required: true
      
      config_format:
        version_change: true
        migration_tool: "config-migrator"
        validation_schema: "config-schema-v3.json"
      
      dependencies:
        - name: "kubernetes"
          min_version: "1.25.0"
          current_compatible: true
        
        - name: "istio"
          min_version: "1.18.0"
          current_compatible: true
        
        - name: "prometheus"
          min_version: "2.40.0"
          current_compatible: true
      
      resource_requirements:
        cpu_increase: "10%"
        memory_increase: "5%"
        storage_increase: "0%"
        network_changes: false
      
      feature_flags:
        - name: "new_slsa_features"
          default_enabled: false
          toggle_required: true
        
        - name: "enhanced_security"
          default_enabled: true
          force_enabled: true
    
    # Pre-upgrade steps
    pre_upgrade_steps:
      - name: "create_backup"
        type: "snapshot"
        description: "Create full system snapshot"
        timeout: "15m"
        critical: true
      
      - name: "validate_cluster"
        type: "validation"
        description: "Validate cluster readiness"
        timeout: "5m"
        critical: true
      
      - name: "drain_traffic"
        type: "traffic_management"
        description: "Gradually drain traffic from nodes"
        timeout: "10m"
        critical: false
    
    # Upgrade steps
    upgrade_steps:
      - name: "update_crds"
        type: "kubernetes"
        description: "Update Custom Resource Definitions"
        timeout: "5m"
        critical: true
      
      - name: "migrate_database"
        type: "database"
        description: "Run database migrations"
        timeout: "20m"
        critical: true
        rollback_sql: "rollback_3.1.0.sql"
      
      - name: "update_configmaps"
        type: "configuration"
        description: "Update configuration maps"
        timeout: "2m"
        critical: true
      
      - name: "rolling_update"
        type: "deployment"
        description: "Perform rolling update"
        timeout: "30m"
        critical: true
        strategy: "canary"
    
    # Post-upgrade steps
    post_upgrade_steps:
      - name: "health_check"
        type: "validation"
        description: "Comprehensive health check"
        timeout: "10m"
        critical: true
      
      - name: "integration_tests"
        type: "testing"
        description: "Run integration test suite"
        timeout: "20m"
        critical: false
      
      - name: "performance_baseline"
        type: "testing"
        description: "Establish new performance baseline"
        timeout: "15m"
        critical: false
    
    # Rollback steps
    rollback_steps:
      - name: "stop_services"
        type: "service_management"
        description: "Stop all services"
        timeout: "5m"
      
      - name: "restore_database"
        type: "database"
        description: "Restore database from backup"
        timeout: "30m"
      
      - name: "restore_configs"
        type: "configuration"
        description: "Restore previous configurations"
        timeout: "5m"
      
      - name: "rollback_deployment"
        type: "deployment"
        description: "Rollback to previous deployment"
        timeout: "15m"
    
    # Validation steps
    validation_steps:
      - name: "api_compatibility"
        type: "api_testing"
        description: "Validate API compatibility"
        timeout: "10m"
      
      - name: "data_integrity"
        type: "data_validation"
        description: "Verify data integrity"
        timeout: "15m"
      
      - name: "performance_regression"
        type: "performance_testing"
        description: "Check for performance regressions"
        timeout: "20m"
    
    # Data migration configuration
    data_migration:
      required: true
      type: "online"  # online, offline
      estimated_duration: "45m"
      
      # Migration steps
      steps:
        - name: "schema_migration"
          description: "Update database schema"
          estimated_duration: "10m"
        
        - name: "data_transformation"
          description: "Transform existing data"
          estimated_duration: "30m"
        
        - name: "index_rebuild"
          description: "Rebuild database indexes"
          estimated_duration: "5m"
      
      # Validation
      validation:
        - "row_count_consistency"
        - "data_integrity_checks"
        - "performance_impact_assessment"
    
    # Downtime window (if required)
    downtime_window:
      required: false
      estimated_duration: "0m"
      maintenance_window:
        start: "02:00 UTC"
        end: "04:00 UTC"
        days: ["sunday"]

# Safety and compliance configuration
safety:
  # SLSA Level 4 requirements
  slsa:
    level: 4
    require_provenance: true
    verify_signatures: true
    hermetic_builds: true
    two_party_review: true
    
    # Build requirements
    builder_requirements:
      isolated_environment: true
      ephemeral_environment: true
      parameterized_triggers: true
      
    # Verification requirements
    verification:
      source_integrity: true
      build_reproducibility: true
      dependency_verification: true
  
  # Security controls
  security:
    # Vulnerability scanning
    vulnerability_scanning:
      enabled: true
      scanners:
        - "trivy"
        - "snyk"
        - "xray"
      fail_on_high: true
      fail_on_critical: true
      
      # Scan triggers
      scan_triggers:
        - "pre_deployment"
        - "scheduled_daily"
        - "on_dependency_update"
    
    # Runtime security
    runtime_security:
      enabled: true
      policies:
        - "no_privileged_containers"
        - "readonly_root_filesystem"
        - "drop_all_capabilities"
        - "run_as_non_root"
      
      # Network policies
      network_policies:
        deny_all_ingress: true
        deny_all_egress: false
        allowed_ingress:
          - from: "istio-system"
          - from: "monitoring"
        allowed_egress:
          - to: "artifactory"
          - to: "dns"
          - to: "external_apis"
    
    # Secret management
    secret_management:
      external_secrets: true
      secret_store: "vault"
      rotation_policy: "30d"
      encryption_at_rest: true
  
  # Compliance requirements
  compliance:
    # Regulatory compliance
    regulations:
      - "SOX"
      - "GDPR"
      - "HIPAA"
      - "PCI-DSS"
    
    # Audit requirements
    audit:
      enabled: true
      log_all_actions: true
      retention_period: "7y"
      immutable_logs: true
      
      # Audit events
      audit_events:
        - "deployment_started"
        - "deployment_completed"
        - "rollback_triggered"
        - "approval_granted"
        - "approval_denied"
        - "configuration_changed"
    
    # Change management
    change_management:
      approval_required: true
      approvers:
        - role: "release_manager"
          required: true
        - role: "security_team"
          required: true
        - role: "platform_team"
          required: true
      
      # Change categories
      change_categories:
        - name: "low_risk"
          auto_approve: true
          conditions:
            - "version_patch_only"
            - "no_database_changes"
            - "no_breaking_changes"
        
        - name: "medium_risk"
          approvers_required: 2
          conditions:
            - "minor_version_change"
            - "database_migrations"
            - "config_changes"
        
        - name: "high_risk"
          approvers_required: 3
          conditions:
            - "major_version_change"
            - "architecture_changes"
            - "security_model_changes"

# Notification configuration
notifications:
  # Notification channels
  channels:
    slack:
      enabled: true
      webhook_url_secret: "slack-webhook"
      channels:
        - name: "#releases"
          events:
            - "deployment_started"
            - "deployment_completed"
            - "deployment_failed"
        - name: "#alerts"
          events:
            - "rollback_triggered"
            - "health_check_failed"
    
    pagerduty:
      enabled: true
      integration_key_secret: "pagerduty-key"
      severity_mapping:
        critical: "critical"
        warning: "warning"
        info: "info"
    
    email:
      enabled: true
      smtp_config_secret: "smtp-config"
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
    
    teams:
      enabled: true
      webhook_url_secret: "teams-webhook"
      channels:
        - name: "Release Management"
          events: ["all"]
  
  # Notification templates
  templates:
    deployment_started:
      title: "🚀 Deployment Started: {{.ReleaseName}} {{.Version}}"
      message: |
        **Release:** {{.ReleaseName}} {{.Version}}
        **Environment:** {{.Environment}}
        **Strategy:** {{.Strategy}}
        **Started by:** {{.Initiator}}
        **Dashboard:** [View Progress]({{.DashboardURL}})
    
    deployment_completed:
      title: "✅ Deployment Completed: {{.ReleaseName}} {{.Version}}"
      message: |
        **Release:** {{.ReleaseName}} {{.Version}}
        **Environment:** {{.Environment}}
        **Duration:** {{.Duration}}
        **Status:** {{.Status}}
        **Metrics:** [View Metrics]({{.MetricsURL}})
    
    rollback_triggered:
      title: "🔄 Rollback Triggered: {{.ReleaseName}}"
      message: |
        **Release:** {{.ReleaseName}} {{.Version}}
        **Environment:** {{.Environment}}
        **Reason:** {{.RollbackReason}}
        **Target:** {{.RollbackTarget}}
        **Triggered by:** {{.Initiator}}
  
  # Escalation policies
  escalation:
    policies:
      - name: "deployment_failure"
        levels:
          - delay: "0m"
            channels: ["slack", "email"]
          - delay: "15m"
            channels: ["pagerduty"]
          - delay: "30m"
            channels: ["pagerduty"]
            escalate_to: "executive_team"
      
      - name: "rollback_failure"
        levels:
          - delay: "0m"
            channels: ["slack", "pagerduty", "email"]
          - delay: "5m"
            channels: ["pagerduty"]
            escalate_to: "incident_commander"

# Environment-specific overrides
environments:
  development:
    strategy:
      auto_promote: true
      auto_rollback: true
    safety:
      approval_required: false
    monitoring:
      alerts:
        enabled: false
    notifications:
      channels:
        slack:
          channels:
            - "#dev-releases"
  
  staging:
    strategy:
      auto_promote: false
      auto_rollback: true
    safety:
      approval_required: true
      approvers: ["platform_team"]
    monitoring:
      alerts:
        enabled: true
        severity_filter: ["critical"]
  
  production:
    strategy:
      auto_promote: false
      auto_rollback: true
    safety:
      approval_required: true
      approvers: ["release_manager", "security_team", "platform_team"]
    monitoring:
      alerts:
        enabled: true
        severity_filter: ["critical", "warning"]
    compliance:
      audit:
        enabled: true
        immutable_logs: true